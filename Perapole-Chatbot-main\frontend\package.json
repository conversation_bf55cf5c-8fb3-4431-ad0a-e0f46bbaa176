{"name": "fuse-react-app", "version": "13.0.0", "private": true, "type": "module", "dependencies": {"@auth/unstorage-adapter": "2.7.4", "@emotion/cache": "11.13.5", "@emotion/react": "11.13.5", "@emotion/styled": "11.13.5", "@hookform/resolvers": "3.6.0", "@mui/base": "5.0.0-beta.64", "@mui/icons-material": "6.3.1", "@mui/material": "6.3.1", "@mui/material-nextjs": "6.1.9", "@mui/styles": "6.1.10", "@mui/system": "6.1.10", "@mui/x-date-pickers": "7.23.1", "@popperjs/core": "2.11.8", "@reduxjs/toolkit": "2.4.0", "@vercel/kv": "1.0.1", "autosuggest-highlight": "3.3.4", "axios": "^1.7.9", "clsx": "2.1.1", "core-js": "3.39.0", "date-fns": "4.1.0", "draft-js": "0.11.7", "draftjs-to-html": "0.9.1", "history": "5.3.0", "i18next": "24.0.5", "keycode": "2.2.1", "lodash": "4.17.21", "marked": "^15.0.6", "material-react-table": "3.0.1", "mobile-detect": "1.4.5", "moment": "2.30.1", "motion": "12.0.0-alpha.2", "next": "15.0.4", "next-auth": "5.0.0-beta.25", "notistack": "3.0.1", "perfect-scrollbar": "1.5.5", "prismjs": "1.29.0", "qs": "6.13.1", "react": "18.2.0", "react-app-alias": "2.2.2", "react-autosuggest": "10.1.0", "react-dom": "18.2.0", "react-draft-wysiwyg": "1.15.0", "react-hook-form": "7.53.2", "react-i18next": "15.1.3", "react-markdown": "latest", "react-popper": "2.3.0", "react-redux": "9.1.2", "react-select-country-list": "^2.2.3", "react-swipeable": "7.0.2", "styled-components": "6.1.13", "stylis": "4.3.4", "stylis-plugin-rtl": "2.1.1", "tailwindcss": "^3.4.17", "type-fest": "4.30.0", "typewriter-effect": "^2.21.0", "unstorage": "1.13.1", "uuid": "11.0.3", "zod": "3.23.8"}, "peerDependencies": {"autoprefixer": "10.4.20", "postcss": "^8.5.1", "react": "18.2.0", "react-dom": "18.2.0"}, "overrides": {"react": "18.2.0", "react-dom": "18.2.0", "redux": "^5.0.1", "semver": "7.5.4"}, "devDependencies": {"@eslint/eslintrc": "3.2.0", "@eslint/js": "9.16.0", "@hookform/devtools": "4.3.1", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/typography": "0.5.15", "@types/autosuggest-highlight": "3.2.3", "@types/draft-js": "0.11.18", "@types/draftjs-to-html": "0.8.4", "@types/lodash": "4.17.4", "@types/node": "22.10.1", "@types/prismjs": "1.26.5", "@types/qs": "6.9.17", "@types/react": "18.3.18", "@types/react-autosuggest": "10.1.11", "@types/react-dom": "18.3.4", "@types/react-draft-wysiwyg": "1.13.8", "@types/react-redux": "7.1.34", "@types/styled-components": "5.1.34", "@typescript-eslint/eslint-plugin": "8.17.0", "autoprefixer": "^10.4.20", "eslint": "9.16.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "7.37.2", "eslint-plugin-react-hooks": "5.1.0", "eslint-plugin-react-refresh": "0.4.16", "eslint-plugin-unused-imports": "4.1.4", "immutable": "4.3.6", "postcss": "^8.5.1", "prettier": "3.2.5", "promise": "8.3.0", "typescript": "5.4.5", "typescript-eslint": "8.17.0", "typescript-plugin-css-modules": "5.1.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint ./src --config ./eslint.config.mjs", "lint:fix": "eslint --fix ./src --config ./eslint.config.mjs", "postinstall": "", "poststart": "node src/utils/node-scripts/fuse-react-message.js", "audit": "npm audit --production"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 3 safari version"]}}