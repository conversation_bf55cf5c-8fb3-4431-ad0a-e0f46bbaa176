"use client";
import { useTheme } from "@mui/material/styles";
import FuseTheme from "@fuse/core/FuseTheme";
import ChatBotContainer from "./ChatBotContainer";
import { useEffect, useState } from "react";
import FuseLoading from "@fuse/core/FuseLoading";

function P6Page() {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);

  const resetChatbot = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/cbot/init`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        console.error("Reset endpoint failed:", response.statusText);
      } else {
        console.log("Chatbot reset successfully.");
      }
    } catch (error) {
      console.error("Error resetting chatbot:", error);
    }
    finally {
      setIsLoading(false);
    }
  };

  // <PERSON>fa yüklendiğinde reset fonksiyonunu çağır
  useEffect(() => {
    resetChatbot();
  }, []);

  if(isLoading){
    return <FuseLoading />
  }

  return (
    <FuseTheme theme={theme} root>
      <ChatBotContainer />
    </FuseTheme>
  );
}

export default P6Page;
