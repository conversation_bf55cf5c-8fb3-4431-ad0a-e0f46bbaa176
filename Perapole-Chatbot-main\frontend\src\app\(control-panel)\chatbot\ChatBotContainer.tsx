"use client";
import React, { useState } from "react";
import { Box, Container, Typography, Card, useTheme, keyframes } from "@mui/material";
import { ChatInput } from "@/components/chatbot/ChatInput";
import axios from "axios";
import ReactMarkdown from "react-markdown";
import { marked } from 'marked';
import Typewriter from 'typewriter-effect';


interface Message {
  id: number;
  text: string;
  timestamp: Date;
  isBot: boolean;
  thinking?: boolean;
  isTyping?: boolean;
}

const pulse = keyframes`
  0% {
    transform: scale(0.95);
    opacity: 0.5;
  }
  
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  
  100% {
    transform: scale(0.95);
    opacity: 0.5;
  }
`;

const ChatBotContainer = () => {
  const theme = useTheme();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: "Mer<PERSON><PERSON>, <PERSON><PERSON> nasıl yardımcı olabilirim?",
      timestamp: new Date(),
      isBot: true,
    },
  ]);
  const [userMessage, setUserMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSendMessage = async () => {    
    if (userMessage.trim()) {
      const currentMessage = userMessage;
      setUserMessage("");
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          text: currentMessage,
          timestamp: new Date(),
          isBot: false,
        },
      ]);

      setMessages((prev) => [
        ...prev,
        {
          id: Date.now() + 1,
          text: "...",
          timestamp: new Date(),
          isBot: true,
          thinking: true,
        },
      ]);

      setLoading(true);
      try {
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_BASE_URL}/cbot/ask`,
          {
            message: userMessage,
          }
        );

        setMessages((prev) => prev.filter((msg) => !msg.thinking));

          const newMessageId = Date.now() + 2;
          setMessages((prev) => [
            ...prev,
            {
              id: newMessageId,
              text: response.data,
              timestamp: new Date(),
              isBot: true,
              isTyping: true,
            },
          ]);
        }
       catch (error) {
        console.error("Error getting response:", error);
        setMessages((prev) => [
          ...prev.filter((msg) => !msg.thinking),
          {
            id: Date.now() + 2,
            text: "Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.",
            timestamp: new Date(),
            isBot: true,
          },
        ]);
      } finally {
        setLoading(false);
      }

      setUserMessage("");
    }
  };

  return (
    <Box
      sx={{
        p: { xs: 1.5, md: 2.5 },
        minHeight: "85vh",
        background:
          theme.palette.mode === "dark"
            ? `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.background.paper} 100%)`
            : "linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%)",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Container
        maxWidth="md"
        sx={{ flex: 1, display: "flex", flexDirection: "column" }}
      >
        <Box sx={{ mb: 2.5 }}>
          <Typography
            variant="h5"
            sx={{ fontWeight: 700, color: "text.primary", mb: 0.5 }}
          >
            PS01
          </Typography>
          {/* <Typography variant="body2" sx={{ color: "text.secondary" }}>
            PeraPole platformu hakkında sorularınızı yanıtlamak için buradayım
          </Typography> */}
        </Box>

        <Card
          sx={{
            flex: 1,
            borderRadius: "24px",
            background:
              theme.palette.mode === "dark"
                ? "rgba(0,0,0,0.6)"
                : "rgba(255,255,255,0.9)",
            backdropFilter: "blur(10px)",
            boxShadow:
              theme.palette.mode === "dark"
                ? "0 4px 24px rgba(0,0,0,0.2)"
                : "0 4px 24px rgba(0,0,0,0.06)",
            display: "flex",
            flexDirection: "column",
            overflow: "hidden",
            position: "relative",
          }}
        >
          <Box
              sx={{
                p: 2,
                flexGrow: 1,
                backgroundColor:
                  theme.palette.mode === "dark"
                    ? theme.palette.background.default
                    : "#F8FAFC",
                overflowY: "auto",
                display: "flex",
                flexDirection: "column",
                gap: 2,
              }}
            >
            {messages.map((msg) => (
                msg.thinking ? (
                  <Box
                    key={msg.id}
                    sx={{
                      p: 1.5,
                      display: 'flex',
                      alignItems: 'center',
                      maxWidth: '80%',
                      alignSelf: 'flex-start',
                    }}
                  >
                    <Box
                      component="span"
                      sx={{
                        width: '12px',
                        height: '12px',
                        borderRadius: '50%',
                        backgroundColor: theme.palette.mode === "dark" ? "#fff" : "#121212",
                        animation: `${pulse} 1.5s infinite ease-in-out`,
                      }}
                    />
                  </Box>
                ) : (
                  <Box
                    key={msg.id}
                    sx={{
                      backgroundColor: msg.isBot
                        ? theme.palette.mode === "dark"
                          ? "rgba(255,255,255,0.05)"
                          : theme.palette.background.paper
                        : theme.palette.mode === "dark"
                          ? "#1E4D8C"
                          : "#64748B",
                      p: 1.5,
                      borderRadius: "12px",
                      boxShadow:
                        theme.palette.mode === "dark"
                          ? "0 2px 8px rgba(0,0,0,0.2)"
                          : "0 2px 8px rgba(0,0,0,0.04)",
                      maxWidth: "80%",
                      maxHeight: "400px",
                      overflow: "auto",
                      alignSelf: msg.isBot ? "flex-start" : "flex-end",
                    }}
                  >
                <Typography
                    component="div"
                    sx={{
                      color: msg.isBot ? "text.primary" : "#fff",
                      mb: 1,
                      wordBreak: "break-word",
                      "& .markdown": {
                        "& p": { margin: 0 },
                        "& code": {
                          backgroundColor: "rgba(0,0,0,0.1)",
                          padding: "2px 4px",
                          borderRadius: "4px",
                        },
                      },
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    }}
                  >
                    {msg.isTyping && (
                        <>
                          <Typewriter
                            onInit={(typewriter) => {
                              const parsedHtml = marked.parse(msg.text, { async: false }) as string;
                              
                              typewriter
                                .changeDelay(3)
                                .typeString(parsedHtml)
                                .callFunction(() => {
                                  setMessages(prev =>
                                    prev.map(m =>
                                      m.id === msg.id ? { ...m, isTyping: false } : m
                                    )
                                  );
                                })
                                .start();
                            }}
                            options={{
                              cursor: '',
                            }}
                          />
                          <Box
                            component="span"
                            sx={{
                              width: '6px',
                              height: '6px',
                              borderRadius: '50%',
                              backgroundColor: theme.palette.mode === "dark" ? "#fff" : "#000",
                              animation: `${pulse} 1.5s infinite ease-in-out`,
                              ml: 1,
                            }}
                          />
                        </>
                      )}
                      {!msg.isTyping && (
                        <ReactMarkdown className="markdown">{msg.text}</ReactMarkdown>
                      )}
                  </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: msg.isBot
                      ? "text.secondary"
                      : "rgba(255,255,255,0.8)",
                    display: "block",
                    textAlign: msg.isBot ? "left" : "right",
                  }}
                >
                  {msg.timestamp.getHours().toString().padStart(2, "0")}:
                  {msg.timestamp.getMinutes().toString().padStart(2, "0")}
                </Typography>
              </Box>
            )))}
          </Box>

          <ChatInput
            value={userMessage}
            onChange={setUserMessage}
            onSend={handleSendMessage}
          />
        </Card>
      </Container>
    </Box>
  );
};

export default ChatBotContainer;
