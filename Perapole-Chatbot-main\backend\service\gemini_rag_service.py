
import os
from typing import override
from dotenv import load_dotenv
import google.generativeai as genai

from service.ai_service import AIService
from service.file_service import FileService

from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.prompts import PromptTemplate
from langchain.chains.combine_documents import create_stuff_documents_chain


class GeminiRagService(AIService):
    def __init__(self, service: FileService):
        load_dotenv()
        self.service = service

        genai.configure(api_key= os.getenv("GOOGLE_API_KEY"))


    def init_vector_store(self, chunks):
        """Create a vector store for semantic search on the text chunks."""
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        self.vector_store = FAISS.from_texts(chunks, embedding=embeddings)


    def init_chain(self):
        """Initialize the QA chain with the updated StuffDocumentsChain."""
        prompt_template = """
        Answer the question as detailed as possible from the provided context, making sure to provide all relevant details.
        If the answer is not in the provided context, just say, "answer is not available in the context". Do not provide a wrong answer.
        
        Context:
        {context}
        
        Question:
        {question}
        
        Answer:
        """

        model = ChatGoogleGenerativeAI(model="gemini-pro", client=genai, temperature=0.3)

        prompt = PromptTemplate(template=prompt_template, input_variables=["context", "question"])

        self.chain = create_stuff_documents_chain(llm=model, prompt=prompt)


    @override
    def start(self):
        self.service.get_text_chunks()
        self.init_vector_store(self.service.chunks)
        self.init_chain()

    @override
    def ask(self, question):
        """Process the user question and return the AI's response."""

        docs = self.vector_store.similarity_search(question, k=7)

        response = self.chain.invoke(
            {"context": docs, "question": question},
            return_only_outputs=True
        )
        return response
