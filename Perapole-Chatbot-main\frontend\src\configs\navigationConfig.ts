import i18n from "@i18n";
import { FuseNavItemType } from "@fuse/core/FuseNavigation/types/FuseNavItemType";
import ar from "./navigation-i18n/ar";
import en from "./navigation-i18n/en";
import tr from "./navigation-i18n/tr";

i18n.addResourceBundle("en", "navigation", en);
i18n.addResourceBundle("tr", "navigation", tr);
i18n.addResourceBundle("ar", "navigation", ar);

//NOT: Navigation yapabilmek için linkler burada tanımlanıyor.

const navigationConfig: FuseNavItemType[] = [
  {
    id: "chatbot",
    title: "PS01",
    translate: "PS01",
    type: "item",
    icon: "heroicons-outline:star",
    url: "chatbot",
  },
];

export default navigationConfig;
