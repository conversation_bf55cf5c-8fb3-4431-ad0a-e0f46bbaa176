'use client';

import React, { useCallback, useEffect, useState } from 'react';
import FuseUtils from '@fuse/utils';
import {
    getSessionRedirectUrl,
    resetSessionRedirectUrl,
    setSessionRedirectUrl
} from '@fuse/core/FuseAuthorization/sessionRedirectUrl';
import { FuseRouteObjectType } from '@fuse/core/FuseLayout/FuseLayout';
import usePathname from '@fuse/hooks/usePathname';
import FuseLoading from '@fuse/core/FuseLoading';
import useNavigate from '@fuse/hooks/useNavigate';

type AuthGuardProps = {
    auth: FuseRouteObjectType['auth'];
    children: React.ReactNode;
    loginRedirectUrl?: string;
};

function AuthGuardRedirect({ auth, children, loginRedirectUrl = '/chatbot' }: AuthGuardProps) {
    const [accessGranted, setAccessGranted] = useState<boolean>(false);
    const navigate = useNavigate();
    const pathname = usePathname();

    const isAuthenticated = () => {
        const token = localStorage.getItem('jwt_token');
        return !!token; // token varsa true, yoksa false döner
    };

    const handleRedirection = useCallback(() => {
        const redirectUrl = getSessionRedirectUrl() || loginRedirectUrl;
        
        if (!isAuthenticated()) {
            navigate('/sign-in');
        } else {
            navigate(redirectUrl);
            resetSessionRedirectUrl();
        }
    }, [loginRedirectUrl]);

    useEffect(() => {
        const ignoredPaths = ['/sign-in', '/404'];

        if (ignoredPaths.includes(pathname)) {
            setAccessGranted(true);
            return;
        }

        if (isAuthenticated()) {
            setAccessGranted(true);
            resetSessionRedirectUrl();
        } else {
            if (!ignoredPaths.includes(pathname)) {
                setSessionRedirectUrl(pathname);
            }
            handleRedirection();
        }
    }, [pathname, handleRedirection]);

    return accessGranted ? children : <FuseLoading />;
}

export default AuthGuardRedirect;