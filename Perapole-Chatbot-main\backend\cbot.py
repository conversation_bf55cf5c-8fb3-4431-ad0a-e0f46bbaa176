# backend/cbot.py

from flask import Blueprint, app, jsonify, request
from service.file_service import FileService
from service.gemini_service import GeminiService

cbot = Blueprint('cbot', __name__)

fileService = FileService("sinpas.pdf")
AIService = GeminiService(fileService)

@cbot.route('/ask', methods=['POST'])
def ask_ai():
    """Endpoint to process user questions."""
    if request.method == 'OPTIONS':
        return jsonify({"message": "CORS allowed"}), 200

    try:
        data = request.get_json()
        question = data.get("message")
        if not question:
            return jsonify({"error": "Please provide a question"}), 400

        response = AIService.ask(question)
        return jsonify(response)
    except Exception as e:
        app.logger.error("Ask Error: %s", e)
        return jsonify({"AI Error": "An error occurred on ask"}), 500


@cbot.route('/init', methods=['POST'])
def init_ai():
    """Endpoint to reinitialize the chatbot (e.g., clearing chat history)."""
    if request.method == 'OPTIONS':
        return jsonify({"message": "CORS allowed"}), 200

    try:
        AIService.start()
        return jsonify({"message": "Chat history cleared and re-initialized."}), 200
    except Exception as e:
        app.logger.error("Init Error: %s", e)
        
        return jsonify({"AI Error": "An error occurred on init"}), 500
