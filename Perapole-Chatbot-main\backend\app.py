import logging
from flask import Flask, jsonify
from flask_cors import CORS
from auth import auth
from cbot import cbot

app = Flask(__name__)
CORS(app)

logging.basicConfig(level=logging.DEBUG)

@app.route('/')
def home():
    return jsonify({
        "message": "Perapole Chatbot API",
        "status": "running",
        "endpoints": {
            "auth": "/auth",
            "chatbot": "/cbot"
        }
    })

app.register_blueprint(auth, url_prefix='/auth')
app.register_blueprint(cbot, url_prefix='/cbot')

if __name__ == '__main__':
    app.run()
