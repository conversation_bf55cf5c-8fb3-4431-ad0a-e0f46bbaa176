# FileService

import os
import re
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>
from langchain_text_splitters import RecursiveCharacterTextSplitter

class FileService():
    
    def __init__(self, filename):
        self.pdf_to_text(filename)

    def pdf_to_text(self, filename):
        """Extract text from the PDF file."""
        path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", filename)
        text = ""
        try:
            pdf_reader = PdfReader(path)
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text

            self.text = self.optimize_text(text)
        except Exception:
            raise


    def optimize_text(self, text):
        """Clean up the extracted text."""
        text = re.sub(r'\s+', ' ', text).strip()
        return text.replace('\n', ' ')


    def get_text_chunks(self):
        """Split the text into manageable chunks."""
        splitter = RecursiveCharacterTextSplitter(chunk_size=10000, chunk_overlap=1000)
        self.chunks = splitter.split_text(self.text)
