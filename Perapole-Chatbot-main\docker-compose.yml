services:
  backend:
    container_name: chatbot-backend
    build:
      context: ./backend
    ports:
      - 5101:5100
    environment:
      FLASK_ENV: production
      GOOGLE_API_KEY: AIzaSyCoCv4P6l5PoxNDSLgtZbK-GqR7ZFj6_kY
    restart: unless-stopped

  frontend:
    container_name: chatbot-frontend
    build:
      context: ./frontend
      args:
        NEXT_PUBLIC_BASE_URL: http://localhost:5101
    ports:
      - 3101:3100
    environment:
      AUTH_SECRET: perapole_super_secret_key_for_nextauth
      NEXTAUTH_URL: http://localhost:3101
    depends_on:
      - backend
    restart: unless-stopped
