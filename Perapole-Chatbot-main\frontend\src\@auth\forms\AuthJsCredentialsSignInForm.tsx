import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import _ from 'lodash';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import Button from '@mui/material/Button';
import { Alert } from '@mui/material';
import useNavigate from '@fuse/hooks/useNavigate';

const NEXT_PUBLIC_BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:5101';
const schema = z.object({
	password: z
		.string()
		.nonempty('Lütfen şifreyi girin.')
});

type FormType = {
	password: string;
};

const defaultValues = {
	password: ''
};

function AuthJsCredentialsSignInForm() {
	const navigate = useNavigate();
	const { control, formState, handleSubmit, setError } = useForm<FormType>({
		mode: 'onChange',
		defaultValues,
		resolver: zodResolver(schema)
	});

	const { isValid, dirtyFields, errors } = formState;

	async function onSubmit(formData: FormType) {
		const { password } = formData;

		try {
			const url = `${NEXT_PUBLIC_BASE_URL}/auth/login`;
			console.log('🔥 Login URL:', url);
			console.log('🔥 NEXT_PUBLIC_BASE_URL:', NEXT_PUBLIC_BASE_URL);
			console.log('🔥 Password:', password);
			alert(`Debug: URL = ${url}`);

			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Authorization': `Basic ${password}`
				}
			});

			const data = await response.json();

			if (response.ok) {
				// JWT token'ı localStorage'a kaydet
				localStorage.setItem('jwt_token', data.token);
				navigate('/chatbot');
				return true;
			} else {
				setError('root', { 
					type: 'manual', 
					message: data.message || 'Geçersiz şifre!'
				});
				return false;
			}
		} catch (error) {
			console.error('Login error:', error);
			setError('root', {
				type: 'manual',
				message: `Bir hata oluştu: ${error.message || 'Lütfen tekrar deneyin.'}`
			});
			return false;
		}
	}

	return (
		<form
			name="loginForm"
			noValidate
			className="mt-32 flex w-full flex-col justify-center"
			onSubmit={handleSubmit(onSubmit)}
		>
			{errors?.root?.message && (
				<Alert
					className="mb-32"
					severity="error"
					sx={(theme) => ({
						backgroundColor: theme.palette.error.light,
						color: theme.palette.error.dark
					})}
				>
					{errors?.root?.message}
				</Alert>
			)}
			
			<Controller
				name="password"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-24"
						label="Şifre"
						type="password"
						error={!!errors.password}
						helperText={errors?.password?.message}
						variant="outlined"
						required
						fullWidth
						autoFocus
					/>
				)}
			/>

			<Button
				variant="contained"
				color="secondary"
				className="mt-16 w-full"
				aria-label="Sign in"
				disabled={_.isEmpty(dirtyFields) || !isValid}
				type="submit"
				size="large"
			>
				Giriş Yap
			</Button>
		</form>
	);
}

export default AuthJsCredentialsSignInForm;