import os
import openai
import numpy as np
import faiss
from dotenv import load_dotenv

from service.ai_service import AIService
from service.file_service import FileService
from langchain.prompts import PromptTemplate
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_openai import OpenAIEmbeddings, ChatOpenAI


class OpenAiRagService(AIService):
    def __init__(self, service: FileService):
        """OpenAI RAG tabanlı hizmetin başlatılması"""
        load_dotenv()
        self.service = service
        openai.api_key = os.getenv("OPENAI_API_KEY")

    def init_vector_store(self, chunks):
        """FAISS ile vektör tabanlı arama oluştur"""
        embeddings = OpenAIEmbeddings(model="text-embedding-ada-002")
        self.embedded_texts = [embeddings.embed_query(chunk) for chunk in chunks]
        
        # FAISS index oluşturma
        dimension = len(self.embedded_texts[0])  # Embedding boyutu
        self.vector_store = faiss.IndexFlatL2(dimension)
        
        # FAISS içine metin embedding'lerini ekleme
        self.vector_store.add(np.array(self.embedded_texts, dtype=np.float32))
        self.text_chunks = chunks

    def init_chain(self):
        """QA için OpenAI modeli ve prompt template oluştur"""
        prompt_template = """
        Answer the question as concisely as possible in the user's language.

        ### **Reference**
        Extract the exact section from the PDF related to the question. No extra explanations, max 200 tokens.

        ### **Translate**
        Translate the referenced text into the user's language.

        Translate also the headers (Reference and Translate) into the user's language.

        Context:
        {context}

        Question:
        {question}

        Response:
        """

        model = ChatOpenAI(model="gpt-4o", temperature=0.3, openai_api_key=openai.api_key)

        prompt = PromptTemplate(template=prompt_template, input_variables=["context", "question"])

        self.chain = create_stuff_documents_chain(llm=model, prompt=prompt)

    def search_similar_chunks(self, query, top_k=5):
        """FAISS ile en yakın metin parçalarını getir"""
        embeddings = OpenAIEmbeddings(model="text-embedding-ada-002")
        query_embedding = np.array([embeddings.embed_query(query)], dtype=np.float32)
        
        distances, indices = self.vector_store.search(query_embedding, k=top_k)
        results = [self.text_chunks[idx] for idx in indices[0] if idx < len(self.text_chunks)]
        
        return results if results else ["answer is not available in the context"]

    def start(self):
        """Servisi başlat ve veriyi FAISS'e yükle"""
        self.service.get_text_chunks()
        self.init_vector_store(self.service.chunks)
        self.init_chain()

    def ask(self, question):
        """Sorulan soruya en iyi cevabı OpenAI RAG ile üret"""
        # Önce özel durumları kontrol edelim
        if any(phrase in question.lower() for phrase in ["sen kimsin", "kim geliştirdi", "ne yapıyorsun", "who are you", "what do you do"]):
            return {
                "response": "Ben PeraPole mühendisleri tarafından geliştirilmiş bir PDF soru-cevaplama chatbotuyum.",
                "reference": None,
                "translate": None
            }

        docs = self.search_similar_chunks(question, k=5)

        response = self.chain.invoke(
            {"context": docs, "question": question},
            return_only_outputs=True
        )

        return {
            "response": response,
            "reference": docs[0] if docs else "answer is not available in the context",
            "translate": "Translation will be automatically handled by the LLM"
        }
