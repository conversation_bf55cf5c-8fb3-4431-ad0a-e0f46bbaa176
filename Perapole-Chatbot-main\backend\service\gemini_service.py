
import os
from typing import override
from dotenv import load_dotenv
import google.generativeai as genai

from service.ai_service import AIService
from service.file_service import FileService

class GeminiService(AIService):

    def __init__(self, service: FileService):
        load_dotenv()
        self.service = service
        genai.configure(api_key= os.getenv("GOOGLE_API_KEY"))
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        self.chat = self.model.start_chat(history=[])


    def initialize_chat(self):
        self.chat.send_message(
            f"""
                You are a multilingual Document Chatbot developed by PeraPole engineers. Your task is to answer questions using only the information provided in the PDF content below. Do not generate or use any external information.

                Rules:

                - Language Support: Always respond in the language of the user's question. Identify the user's language if it is not explicitly provided.
                - Answer Source: Base your answers strictly on the provided PDF content. Do not make assumptions or extrapolate information.
                - Fallback Message: If the answer is not found in the PDF, reply with the following fallback message in the user's language: "I'm sorry, but the answer to your question is not contained within the provided document." Translate this message into other languages as needed and use the appropriate translation. For example, in Turkish: "Üzgünüm, sorunuzun cevabı verilen belgede bulunmamaktadır."
                - Conciseness: Provide concise and direct answers. Avoid unnecessary explanations or summaries unless specifically asked for.
                - Citation: If possible, subtly indicate the part of the document your answer is based on. This could be a page number, section title, or a short quote. For example: "(pg. 3)" or "(Section: Introduction)" or "According to the document, '...'"
                - No External Information: Do not access or use any information outside of the provided PDF content.

                PDF Content:
                {self.service.text}
            """
        )



    def generate_prompt(self, question):
        return f"""
        User's Question:
        {question}

        """
    

    @override
    def start(self):
        self.chat = self.model.start_chat(history=[])
        self.initialize_chat()


    @override
    def ask(self, question):

        prompt = self.generate_prompt(question)
        response = self.chat.send_message(prompt)
        return response.text
        