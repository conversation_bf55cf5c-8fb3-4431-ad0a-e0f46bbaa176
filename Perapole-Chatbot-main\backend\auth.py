# backend/auth.py

from flask import Blueprint, request, jsonify
import jwt
from werkzeug.security import check_password_hash, generate_password_hash
from functools import wraps
from datetime import datetime, timedelta

auth = Blueprint('auth', __name__)

CORRECT_PASSWORD = "Perapole2025!"
PASSWORD_HASH = generate_password_hash(CORRECT_PASSWORD)
JWT_SECRET = "perapole_super_secret_key"

def create_token():
    exp_time = datetime.utcnow() + timedelta(hours=24)
    return jwt.encode(
        {'exp': exp_time},
        JWT_SECRET,
        algorithm='HS256'
    )

def verify_token(token):
    try:
        jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return True
    except:
        return False

def require_auth(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        
        if not auth_header:
            return jsonify({'message': 'Token gerekli!'}), 401
        
        try:
            if auth_header.startswith('Basic '):
                password = auth_header.split(' ')[1]
                if not check_password_hash(PASSWORD_HASH, password):
                    return jsonify({'message': 'Geçersiz şifre!'}), 401
                    
            elif auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                if not verify_token(token):
                    return jsonify({'message': 'Geçersiz veya süresi dolmuş token!'}), 401
                    
            else:
                return jsonify({'message': 'Geçersiz yetkilendirme başlığı!'}), 401
                
        except Exception:
            return jsonify({'message': 'Geçersiz kimlik doğrulama bilgileri!'}), 401
            
        return f(*args, **kwargs)
    return decorated

@auth.route('/login', methods=['POST'])
def login():
    auth_header = request.headers.get('Authorization')
    print("Auth header:", auth_header)
    
    if not auth_header or not auth_header.startswith('Basic '):
        return jsonify({'message': 'Şifre gerekli!'}), 401
        
    try:
        password = auth_header.split(' ')[1]
        print("Password:", password)
        
        if check_password_hash(PASSWORD_HASH, password):
            token = create_token()
            return jsonify({
                'token': token,
                'message': 'Giriş başarılı!'
            })
        else:
            return jsonify({'message': 'Geçersiz şifre!'}), 401
            
    except Exception as e:
        print("Hata:", str(e))
        return jsonify({'message': 'Geçersiz kimlik doğrulama bilgileri!'}), 401

@auth.route('/protected')
@require_auth
def protected():
    return jsonify({'message': 'Bu gizli bir endpoint!'})