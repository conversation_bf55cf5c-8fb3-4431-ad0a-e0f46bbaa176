import FuseScrollbars from "@fuse/core/FuseScrollbars";
import { styled } from "@mui/material/styles";
import clsx from "clsx";
import { memo } from "react";
import Navigation from "src/components/theme-layouts/components/navigation/Navigation";
import UserMenu from "src/components/theme-layouts/components/UserMenu";
import { Divider } from "@mui/material";
import Logo from "../../../../components/Logo";
// import GoToDocBox from "@/components/theme-layouts/components/GoToDocBox";

const Root = styled("div")(({ theme }) => ({
  backgroundColor: theme.palette.background.default,
  color: theme.palette.text.primary,
  "& ::-webkit-scrollbar-thumb": {
    boxShadow: `inset 0 0 0 20px ${"rgba(255, 255, 255, 0.24)"}`,
    ...theme.applyStyles("light", {
      boxShadow: `inset 0 0 0 20px ${"rgba(0, 0, 0, 0.24)"}`,
    }),
  },
  "& ::-webkit-scrollbar-thumb:active": {
    boxShadow: `inset 0 0 0 20px ${"rgba(255, 255, 255, 0.37)"}`,
    ...theme.applyStyles("light", {
      boxShadow: `inset 0 0 0 20px ${"rgba(0, 0, 0, 0.37)"}`,
    }),
  },
}));

const StyledContent = styled(FuseScrollbars)(() => ({
  overscrollBehavior: "contain",
  overflowX: "hidden",
  overflowY: "auto",
  WebkitOverflowScrolling: "touch",
  backgroundRepeat: "no-repeat",
  backgroundSize: "100% 40px, 100% 10px",
  backgroundAttachment: "local, scroll",
}));

type NavbarStyle1ContentProps = {
  className?: string;
};

/**
 * The navbar style 1 content.
 */
function NavbarStyle1Content(props: NavbarStyle1ContentProps) {
  const { className = "" } = props;

  return (
    <Root
      className={clsx(
        "flex h-full flex-auto flex-col overflow-hidden",
        className
      )}
    >
      <div className="flex h-48 shrink-0 flex-row items-center px-12 md:h-72">
        <Logo />
      </div>

      <StyledContent
        className="flex min-h-0 flex-1 flex-col"
        option={{ suppressScrollX: true, wheelPropagation: false }}
      >
        <Navigation layout="vertical" />

        <div className="flex-0 flex items-center justify-center py-48 opacity-10">
          <img
            className="w-full max-w-64"
            src="/assets/images/logo/perapole.png"
            alt="footer logo"
          />
        </div>
      </StyledContent>

      {/* Not: Documentation linki */}
      {/* <GoToDocBox className="mx-12 my-16" /> */}

      <Divider />

      <div className="p-4 md:p-16 w-full">
        <UserMenu className="w-full" />
      </div>
    </Root>
  );
}

export default memo(NavbarStyle1Content);
