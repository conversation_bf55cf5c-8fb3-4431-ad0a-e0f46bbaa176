import { useState, useEffect, useMemo } from 'react';
import _ from 'lodash';
import setIn from '@/utils/setIn';
import { User } from '@auth/user';
import useNavigate from '@fuse/hooks/useNavigate';

type UseUser = {
    data: User | null;
    isGuest: boolean;
    updateUser: (updates: Partial<User>) => Promise<User | undefined>;
    updateUserSettings: (newSettings: User['settings']) => Promise<User['settings'] | undefined>;
    signOut: () => void;
};

function useUser(): UseUser {
    const navigate = useNavigate();
    const [user, setUser] = useState<User | null>(null);

    // Basit bir user objesi oluştur
    useEffect(() => {
        const token = localStorage.getItem('jwt_token');
        if (token) {
            setUser({
				id: '1',  // id alanını ekledik
				role: ['admin'],
				displayName: 'PeraPole Admin', // üst seviyede displayName gerekiyor
				data: {
					displayName: 'PeraPole Admin',
					photoURL: '/assets/images/logo/perapole.png',
					email: '<EMAIL>',
					shortcuts: []
				},
				settings: {
					theme: {},
					layout: {},
					navbar: {}
				}
			} as User);
        } else {
            setUser(null);
        }
    }, []);

    const isGuest = useMemo(() => !user?.role || user?.role?.length === 0, [user]);

    async function handleUpdateUser(_data: Partial<User>) {
        // Şu an için basit bir güncelleme
        const updatedUser = { ...user, ..._data } as User;
        setUser(updatedUser);
        return updatedUser;
    }

    async function handleUpdateUserSettings(newSettings: User['settings']) {
        const newUser = setIn(user, 'settings', newSettings) as User;

        if (_.isEqual(user, newUser)) {
            return undefined;
        }

        const updatedUser = await handleUpdateUser(newUser);
        return updatedUser?.settings;
    }

    function handleSignOut() {
        localStorage.removeItem('jwt_token');
        setUser(null);
        navigate('/sign-in');
    }

    return {
        data: user,
        isGuest,
        signOut: handleSignOut,
        updateUser: handleUpdateUser,
        updateUserSettings: handleUpdateUserSettings
    };
}

export default useUser;